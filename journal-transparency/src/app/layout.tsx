import type { <PERSON>ada<PERSON>, Viewport } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Mendelity Journal Watch - Find the Right Journal for Your Research",
  description: "Compare journal impact metrics, Article Processing Charges (APC), and open access status. Interactive scatter plot and filtering tools help scientists choose the best journal for their research. Includes data from 30,000+ journals with PhD salary reference lines.",
  keywords: "journal selection, APC costs, impact factor, open access, research publishing, SJR, scientific journals, article processing charges, PhD stipend, professor salary",
  authors: [{ name: "Mendelity Journal Watch" }],
  creator: "Mendelity Journal Watch",
  publisher: "Mendelity Journal Watch",
  robots: "index, follow",
  openGraph: {
    title: "Mendelity Journal Watch - Find the Right Journal for Your Research",
    description: "Compare journal impact metrics, APC costs, and open access status for 30,000+ journals. Interactive tools help scientists make informed publishing decisions.",
    type: "website",
    locale: "en_US",
    siteName: "Mendelity Journal Watch",
  },
  twitter: {
    card: "summary_large_image",
    title: "Mendelity Journal Watch - Find the Right Journal for Your Research",
    description: "Compare journal impact metrics, APC costs, and open access status for 30,000+ journals.",
  },

};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  themeColor: '#ffffff',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="canonical" href="https://journalwatch.mendelity.com" />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebApplication",
              "name": "Mendelity Journal Watch",
              "description": "Compare journal impact metrics, Article Processing Charges (APC), and open access status for 30,000+ journals",
              "url": "https://journalwatch.mendelity.com",
              "applicationCategory": "ResearchTool",
              "operatingSystem": "Web Browser",
              "offers": {
                "@type": "Offer",
                "price": "0",
                "priceCurrency": "USD"
              },
              "creator": {
                "@type": "Organization",
                "name": "Mendelity Journal Watch"
              }
            })
          }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
