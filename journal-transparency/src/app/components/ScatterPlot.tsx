/**
 * Scatter plot component for Journal Choice Transparency application.
 */

'use client';

import React, { useMemo } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>A<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  ResponsiveContainer,
  Legend,
  ReferenceLine,
} from 'recharts';
import { useStore } from '../store';

const OA_TYPE_COLORS = {
  'Fully OA': '#10B981', // Green
  'Hybrid': '#F59E0B',   // Amber
  'Diamond': '#8B5CF6',  // Purple
  'Subscription': '#EF4444', // Red
  'Unknown': '#6B7280',  // Gray
};

// Salary reference lines (monthly amounts in USD)
const SALARY_REFERENCES = {
  'USA PhD Salary': 2750,
  'Germany PhD Salary': 2000,
  'China PhD Salary': 500,
  'USA Professor Salary': 9580,
};

const SALARY_COLORS = {
  'USA PhD Salary': '#1E40AF',      // Blue
  'Germany PhD Salary': '#DC2626',  // Red
  'China PhD Salary': '#059669',    // Green
  'USA Professor Salary': '#7C3AED', // Purple
};

const ScatterPlot: React.FC = React.memo(() => {
  const { filteredJournals, selectedJournals, addSelectedJournal, removeSelectedJournal } = useStore();

  // Prepare data for scatter plot - only show journals with APC data
  const scatterData = useMemo(() => {
    // Filter out journals with null APC for scatter plot
    const journalsWithAPC = filteredJournals.filter(j =>
      j.apc_usd != null &&
      !isNaN(j.apc_usd) &&
      j.apc_usd > 0 &&
      j.impact_proxy != null &&
      !isNaN(j.impact_proxy)
    );

    // Sample for performance if too many points, but maintain consistency
    let journals = journalsWithAPC;
    if (journals.length > 5000) {
      // Sort by impact to ensure consistent sampling and take top journals
      const sorted = [...journals].sort((a, b) => b.impact_proxy - a.impact_proxy);
      journals = sorted.slice(0, 5000);
    }

    return journals.map((journal) => ({
      x: journal.impact_proxy,
      y: journal.apc_usd,
      title: journal.title,
      oa_type: journal.oa_type,
      cost_efficiency: typeof journal.cost_efficiency === 'number' && journal.cost_efficiency === 999999 ? Infinity : journal.cost_efficiency,
      issn_l: journal.issn_l,
      publisher: journal.publisher,
      selected: selectedJournals.has(journal.issn_l),
    }));
  }, [filteredJournals, selectedJournals]);

  // Group data by OA type (include all points, selected and unselected)
  const dataByOAType = useMemo(() => {
    const grouped: Record<string, typeof scatterData> = {};

    scatterData.forEach((point) => {
      const key = point.oa_type;
      if (!grouped[key]) {
        grouped[key] = [];
      }
      grouped[key].push(point);
    });

    return grouped;
  }, [scatterData]);

  const CustomTooltip = ({ active, payload }: { active?: boolean; payload?: Array<{ payload: { title: string; x: number; y: number; oa_type: string; cost_efficiency: number; issn_l: string; publisher: string } }> }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-300 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-900 mb-1">{data.title}</p>
          <p className="text-sm text-gray-600">SJR: {data.x?.toFixed(2) || 'N/A'}</p>
          <p className="text-sm text-gray-600">APC: ${data.y?.toLocaleString() || 'N/A'}</p>
          <p className="text-sm text-gray-600">OA Type: {data.oa_type}</p>
          <p className="text-sm text-gray-600">Publisher: {data.publisher}</p>
          <p className="text-sm text-gray-600">
            Cost Efficiency: {
              data.cost_efficiency === Infinity
                ? '∞'
                : data.cost_efficiency?.toFixed(4) || 'N/A'
            }
          </p>
        </div>
      );
    }
    return null;
  };

  const handlePointClick = (data: { issn_l?: string }) => {
    if (data && data.issn_l) {
      if (selectedJournals.has(data.issn_l)) {
        removeSelectedJournal(data.issn_l);
      } else {
        addSelectedJournal(data.issn_l);
      }
    }
  };

  // Custom dot component that handles selection state
  const CustomDot = (props: {
    cx?: number;
    cy?: number;
    payload?: {
      selected?: boolean;
      oa_type?: string;
      issn_l?: string;
    };
  }) => {
    const { cx, cy, payload } = props;
    const isSelected = payload?.selected;
    const radius = isSelected ? 4 : 3;
    const fillColor = OA_TYPE_COLORS[payload?.oa_type as keyof typeof OA_TYPE_COLORS] || '#6B7280';

    return (
      <circle
        cx={cx}
        cy={cy}
        r={radius}
        fill={fillColor}
        stroke={isSelected ? "#000000" : "none"}
        strokeWidth={isSelected ? 2 : 0}
        style={{ cursor: 'pointer' }}
        onClick={() => handlePointClick(payload || {})}
      />
    );
  };

  return (
    <div className="h-full">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">
          SJR vs APC Cost
        </h3>
        <div className="text-sm text-gray-600">
          {scatterData.length} journals with APC data (of {filteredJournals.length} total)
        </div>
      </div>

      <ResponsiveContainer width="100%" height="90%">
        <ScatterChart
          margin={{
            top: 20,
            right: 120,
            bottom: 60,
            left: 60,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis
            type="number"
            dataKey="x"
            name="SJR"
            domain={[0, 'dataMax']}
            label={{ value: 'SJR (SCImago Journal Rank)', position: 'insideBottom', offset: -10 }}
          />
          <YAxis
            type="number"
            dataKey="y"
            name="APC (Article Processing Charge, USD)"
            domain={[0, 'dataMax']}
            label={{
              value: 'APC (Article Processing Charge, USD)',
              angle: -90,
              position: 'insideLeft',
              style: { textAnchor: 'middle' },
              offset: -10
            }}
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend
            verticalAlign="middle"
            align="right"
            layout="vertical"
            iconType="circle"
            wrapperStyle={{ paddingLeft: '20px' }}
          />

          {/* Salary reference lines */}
          {Object.entries(SALARY_REFERENCES).map(([label, amount]) => (
            <ReferenceLine
              key={label}
              y={amount}
              stroke={SALARY_COLORS[label as keyof typeof SALARY_COLORS]}
              strokeDasharray="5 5"
              strokeWidth={2}
              label={{
                value: `${label} (~$${amount}/mo)`,
                position: 'insideTopRight',
                style: { fontSize: '12px', fill: SALARY_COLORS[label as keyof typeof SALARY_COLORS] }
              }}
            />
          ))}

          {/* Data points with custom dot handling selection state */}
          {Object.entries(dataByOAType).map(([oaType, data]) => (
            <Scatter
              key={oaType}
              name={oaType}
              data={data}
              fill={OA_TYPE_COLORS[oaType as keyof typeof OA_TYPE_COLORS] || '#6B7280'}
              shape={<CustomDot />}
            />
          ))}
        </ScatterChart>
      </ResponsiveContainer>

      <div className="mt-4 text-xs text-gray-500">
        Click points to select journals. Selected journals will be highlighted in the table below.
      </div>
    </div>
  );
});

ScatterPlot.displayName = 'ScatterPlot';

export default ScatterPlot;
