/**
 * Zustand store for Journal Choice Transparency application.
 */

import { create } from 'zustand';
import { Journal, FilterState, AppState } from './types';

// Debounce function for search optimization
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

interface StoreActions {
  setJournals: (journals: Journal[]) => void;
  setFilters: (filters: Partial<FilterState>) => void;
  setSelectedJournals: (selected: Set<string>) => void;
  addSelectedJournal: (issn: string) => void;
  removeSelectedJournal: (issn: string) => void;
  clearSelectedJournals: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  applyFilters: () => void;
}

type Store = AppState & StoreActions;

const initialFilters: FilterState = {
  oaTypes: new Set(),
  apcRange: [0, 15000], // Increased to include all journals (max APC is ~11,400)
  impactRange: [0, 150], // Increased to include high-impact journals like Cell (22.6) and Nature (18.3)
  searchTerm: '',
  includeNaApc: false,
};

export const useStore = create<Store>((set, get) => ({
  // State
  journals: [],
  filteredJournals: [],
  selectedJournals: new Set(),
  filters: initialFilters,
  isLoading: false,
  error: null,

  // Actions
  setJournals: (journals) => {
    set({ journals });
    get().applyFilters();
  },

  setFilters: (newFilters) => {
    const currentFilters = get().filters;
    const updatedFilters = { ...currentFilters, ...newFilters };
    set({ filters: updatedFilters });
    // Debounce filter application for better performance
    setTimeout(() => get().applyFilters(), 100);
  },

  setSelectedJournals: (selected) => set({ selectedJournals: selected }),

  addSelectedJournal: (issn) => {
    const current = get().selectedJournals;
    const updated = new Set(current);
    updated.add(issn);
    set({ selectedJournals: updated });
  },

  removeSelectedJournal: (issn) => {
    const current = get().selectedJournals;
    const updated = new Set(current);
    updated.delete(issn);
    set({ selectedJournals: updated });
  },

  clearSelectedJournals: () => set({ selectedJournals: new Set() }),

  setLoading: (isLoading) => set({ isLoading }),

  setError: (error) => set({ error }),

  applyFilters: () => {
    const { journals, filters } = get();

    // Pre-compute values for performance
    const hasOAFilter = filters.oaTypes.size > 0;
    const searchTermLower = filters.searchTerm.toLowerCase();
    const hasSearchTerm = searchTermLower.length > 0;
    const [apcMin, apcMax] = filters.apcRange;
    const [impactMin, impactMax] = filters.impactRange;

    const filtered = journals.filter((journal) => {
      // Early returns for better performance

      // OA Type filter (most selective first)
      if (hasOAFilter && !filters.oaTypes.has(journal.oa_type)) {
        return false;
      }

      // Impact proxy range filter (numeric comparison is fast)
      if (journal.impact_proxy < impactMin || journal.impact_proxy > impactMax) {
        return false;
      }

      // APC range filter - handle null values
      if (journal.apc_usd != null) {
        if (journal.apc_usd < apcMin || journal.apc_usd > apcMax) {
          return false;
        }
      } else if (!filters.includeNaApc) {
        return false;
      }

      // Search term filter (most expensive, do last)
      if (hasSearchTerm && !journal.title.toLowerCase().includes(searchTermLower)) {
        return false;
      }

      return true;
    });

    set({ filteredJournals: filtered });
  },
}));
