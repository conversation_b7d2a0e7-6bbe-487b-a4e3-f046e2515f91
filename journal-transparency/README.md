# Journal Choice Transparency

A web application that helps scientists choose the right journal for their research based on impact metrics, Article Processing Charges (APC), and open access status.

## Features

- **Interactive Scatter Plot**: Visualize the relationship between journal impact and APC costs
- **Advanced Filtering**: Filter journals by subject area, open access type, APC range, and impact metrics
- **Smart Recommendations**: Get personalized journal recommendations based on your criteria
- **Data Export**: Export filtered or selected journals to CSV
- **Responsive Design**: Works seamlessly on desktop and mobile devices

## Data Sources

- **SciMago Journal Rankings**: Impact metrics and journal metadata
- **MDPI**: Article Processing Charges for MDPI journals
- **OpenAPC**: Historical APC data from various publishers
- **DOAJ**: Directory of Open Access Journals

## Technology Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS
- **Charts**: Recharts
- **Tables**: TanStack Table
- **State Management**: Zustand
- **Data Processing**: Python with pandas

## Getting Started

### Prerequisites

- Node.js 18+
- Python 3.8+
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd journal-transparency
```

2. Install dependencies:
```bash
npm install
```

3. Process the data (if needed):
```bash
cd ..
python data_processor.py
cp public/journals_mvp.json journal-transparency/public/
```

4. Run the development server:
```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Usage

1. **Browse Journals**: View all journals in the scatter plot and table
2. **Apply Filters**: Use the filter panel to narrow down journals by:
   - Subject area
   - Open access type
   - APC price range
   - Minimum impact threshold
   - Journal title search
3. **Get Recommendations**: View personalized recommendations based on your filters
4. **Select Journals**: Click on points in the scatter plot or rows in the table to select journals
5. **Export Data**: Export your filtered or selected journals to CSV

## Project Structure

```
journal-transparency/
├── src/app/
│   ├── components/          # React components
│   │   ├── DataTable.tsx    # Journal data table
│   │   ├── FilterPanel.tsx  # Filter controls
│   │   ├── ScatterPlot.tsx  # Interactive scatter plot
│   │   ├── Header.tsx       # Page header
│   │   ├── Footer.tsx       # Page footer
│   │   ├── SummaryStats.tsx # Statistics cards
│   │   ├── Recommendations.tsx # Smart recommendations
│   │   ├── ExportButton.tsx # CSV export functionality
│   │   └── LoadingSkeleton.tsx # Loading state
│   ├── store.ts             # Zustand state management
│   ├── types.ts             # TypeScript type definitions
│   └── page.tsx             # Main page component
├── public/
│   └── journals_mvp.json    # Processed journal data
└── data_processor.py        # Python data processing script
```

## Deployment & Updates

### Building for Production

1. **Build the static site:**
```bash
npm run build
```

2. **Export static files:**
```bash
npm run export
```

This creates an `out/` directory with all static files ready for deployment.

### Deploying to External Hosting (Hostinger, Netlify, Vercel, etc.)

#### Option 1: Hostinger File Manager

1. **Build the project locally:**
```bash
npm run build
npm run export
```

2. **Upload files:**
   - Log into your Hostinger control panel
   - Go to File Manager
   - Navigate to your domain's public_html folder
   - Upload all contents from the `out/` directory
   - Ensure the `journals_mvp.json` file is in the root directory

3. **Set up custom domain (if needed):**
   - Configure your domain to point to the hosting directory
   - Update any absolute URLs in the code if necessary

#### Option 2: Git-based Deployment (Recommended)

1. **Set up automatic deployment:**
   - Connect your repository to Netlify, Vercel, or similar service
   - Configure build command: `npm run build && npm run export`
   - Set publish directory: `out`

2. **Environment setup:**
   - Ensure Node.js 18+ is available in the build environment
   - No environment variables needed for basic deployment

#### Option 3: Manual FTP/SFTP Upload

1. **Build locally:**
```bash
npm run build
npm run export
```

2. **Upload via FTP:**
   - Use an FTP client (FileZilla, WinSCP, etc.)
   - Upload all files from `out/` directory to your web root
   - Maintain directory structure

### Updating the Website with New Data

#### Method 1: Complete Update (Recommended)

1. **Update source data:**
   - Add new CSV files to `source_data/` directory
   - Ensure files follow the expected format (see existing files as examples)

2. **Reprocess data:**
```bash
# From the project root directory
python data_processor.py
```

3. **Copy updated data:**
```bash
cp public/journals_mvp.json journal-transparency/public/
```

4. **Rebuild and deploy:**
```bash
cd journal-transparency
npm run build
npm run export
# Upload the new 'out/' directory contents to your hosting
```

#### Method 2: Data-Only Update

If you only need to update the journal data without code changes:

1. **Process new data:**
```bash
python data_processor.py
```

2. **Upload only the data file:**
   - Upload the new `public/journals_mvp.json` to your hosting
   - Place it in the same directory as your website files
   - The website will automatically use the updated data

### Automated Update Workflow

For regular updates, consider setting up automation:

1. **GitHub Actions (if using GitHub):**
```yaml
# .github/workflows/update-data.yml
name: Update Journal Data
on:
  schedule:
    - cron: '0 0 1 * *'  # Monthly on 1st day
  workflow_dispatch:

jobs:
  update:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'
      - name: Install dependencies
        run: pip install pandas numpy
      - name: Process data
        run: python data_processor.py
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install and build
        run: |
          cd journal-transparency
          npm install
          cp ../public/journals_mvp.json public/
          npm run build
          npm run export
      - name: Deploy
        # Add your deployment step here
```

2. **Local automation script:**
```bash
#!/bin/bash
# update-and-deploy.sh
echo "Updating journal data..."
python data_processor.py
cp public/journals_mvp.json journal-transparency/public/

echo "Building website..."
cd journal-transparency
npm run build
npm run export

echo "Ready for deployment - upload 'out/' directory contents"
```

### Troubleshooting Deployment

**Common Issues:**

1. **Missing data file:**
   - Ensure `journals_mvp.json` is in the correct location
   - Check file permissions on the hosting server

2. **Build failures:**
   - Verify Node.js version (18+ required)
   - Clear npm cache: `npm cache clean --force`
   - Delete `node_modules` and reinstall: `rm -rf node_modules && npm install`

3. **Large file size:**
   - The JSON file may be large (~2-5MB)
   - Ensure your hosting plan supports the file size
   - Consider enabling gzip compression on your server

4. **CORS issues:**
   - If loading data fails, check browser console for CORS errors
   - Ensure the JSON file is served with correct headers

### Performance Optimization

1. **Enable compression:**
   - Configure your hosting to serve files with gzip compression
   - This can reduce the JSON file size by 70-80%

2. **CDN setup:**
   - Use a CDN (Cloudflare, etc.) to cache static files
   - Improves loading speed globally

3. **Caching headers:**
   - Set appropriate cache headers for static assets
   - Cache JSON data for reasonable periods (e.g., 1 hour)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Acknowledgments

- SciMago for journal ranking data
- MDPI for APC transparency
- OpenAPC initiative for open APC data
- DOAJ for open access journal directory
