{"name": "journal-transparency", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.12", "lucide-react": "^0.525.0", "next": "15.4.2", "react": "19.1.0", "react-dom": "19.1.0", "recharts": "^3.1.0", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.2", "tailwindcss": "^4", "typescript": "^5"}}