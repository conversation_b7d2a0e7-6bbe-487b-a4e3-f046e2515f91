Build a *Journal Choice Transparency* website focused on **one page (or two)** that helps scientists choose a journal using:

1. **A sortable / filterable table** (core metadata, APC price, OA status, impact metric).
2. **An interactive scatter plot** (X: impact, Y: APC price) with tooltips & selection syncing to the table.

## 1. MVP VALUE PROPOSITION

**Question answered:** “Given my manuscript & budget, which journals balance impact and cost / openness?”

**Primary Actions for User:**

1. Filter by subject (e.g., Oncology, Genomics).
2. Set max APC (slider).
3. Choose OA status (Fully OA / Hybrid / Subscription / Diamond).
4. Sort by: Impact, APC, Cost‑Efficiency (impact/APC).
5. Visually inspect scatter: identify outliers (high impact & lower APC).

---

## 2. CORE DATA FIELDS

| Field                      | Purpose       | Source (Open Preferred)                                                         | Notes                                            |
| -------------------------- | ------------- | ------------------------------------------------------------------------------- | ------------------------------------------------ |
| Journal Title              | Display       | DOAJ / OpenAlex                                                                 | Canonical casing.                                |
| ISSN-L                     | Key           | DOAJ / OpenAlex                                                                 | Hidden column.                                   |
| Subject Category (primary) | Filter        | OpenAlex concepts or DOAJ subjects                                              | Map to controlled list (top-level).              |
| Impact Proxy               | X-axis        | OpenAlex: 2-yr citations per doc or Field Citation Ratio alt; OR SJR if allowed | Use an *open* metric to avoid license issues.    |
| APC (USD)                  | Y-axis        | OpenAPC (historical) + direct scrape fallback                                   | Convert & store date.                            |
| OA Type                    | Filter        | DOAJ + OpenAPC + Unpaywall aggregation                                          | Values: Fully OA, Hybrid, Diamond, Subscription. |
| License Options            | Transparency  | DOAJ / publisher page                                                           | Show primary (e.g., CC-BY).                      |
| Waiver Policy Flag         | Equity signal | Publisher (manual/ scraped) optional                                            | Boolean in MVP (Y/N).                            |
| Link to Journal Site       | Action        | DOAJ / metadata                                                                 | New tab.                                         |
| Last Verified (date)       | Trust         | Derived                                                                         | Show as tooltip badge.                           |

**Derived Field:** *Cost Efficiency* = Impact Proxy / APC (if APC > 0, else “∞” for Diamond).

---

## 3. DATA INGESTION (MVP)

* APC & Impact data of major publishers are already in source_data, pdf & xlsx, may need parsing to csv
* pull other data as needed
**Result:** Single tidy Parquet/JSON file (e.g., `journals_mvp.parquet`) served to frontend.

---

## 4. SIMPLE SCHEMA (POST-MERGE)

```json
{
  "issn_l": "1234-5678",
  "title": "Journal of X",
  "subject": "Oncology",
  "impact_proxy": 3.45,
  "apc_usd": 2500,
  "oa_type": "Hybrid",
  "license": "CC-BY",
  "waiver": true,
  "cost_efficiency": 0.00138,
  "last_verified": "2025-07-15",
  "journal_url": "https://journalofx.org"
}
```

---

## 5. FRONTEND (ONE MAIN PAGE)

**Tech:** Next.js + TypeScript + Tailwind + a lightweight chart library (e.g., `visx` or `Plotly` if speed > bundle size less critical).

**Layout (Desktop):**

```
-----------------------------------------------------------
Header (Title + short tagline + link to About/Data Method)
Filter Panel (left column) | Scatter Plot (top-right)
                           | Data Table (bottom-right)
-----------------------------------------------------------
Footer (Data Sources & Update timestamp)
```

**Filter Panel Controls:**

* Subject (multi-select, search).
* OA Type (checkbox group).
* APC (range slider: 0 – max).
* Impact Proxy (min threshold).
* Search box (title contains).

**Scatter Plot:**

* X = Impact Proxy (log scale toggle).
* Y = APC USD (log scale toggle).
* Point color by OA Type (e.g., Fully OA, Hybrid, Diamond, Subscription).
* Point size optional (e.g., works\_count^0.3).
* Tooltip: Title, APC, Impact, OA Type, Cost Efficiency.
* Brushing/drag box selects subset → highlights rows in table (and vice versa on row hover).

**Data Table:**

* Virtualized (e.g., `react-virtualized`) for performance.
* Columns: Title (link), APC (USD), Impact, Cost Efficiency, OA Type, License, Last Verified badge.
* Sortable on any numeric column.
* Column for “Diamond” shows APC as 0 and cost efficiency = “∞”.

**State Sync:**

* Global context store (Zustand/React context).
* Selected journals in scatter update table highlight & enable “Export CSV (selected)”.

---

## 6. BACKEND (MVP LIGHT)

* **Static Hosting Option:** Pre-generate JSON/Parquet and serve via CDN (no dynamic server required at first).
* **API Option (if dynamic filtering needed server-side):** FastAPI endpoint `/journals` with query params:

  * `subject=Oncology&oa_type=Hybrid&apc_max=3000&impact_min=2&page=1&sort=impact_proxy:desc`
* **Caching:** Cloudflare edge cache for JSON (cache-busting via file hash in filename).
* **No DB initially:** Just regenerate dataset monthly; move to Postgres only if user-submitted corrections introduced.



## KEY FUNCTIONS (PSEUDOCODE SNAPSHOT)

**ETL Merge (Python):**

```python
# load openalex, doaj, openapc into dataframes (oa_df, dj_df, apc_df)
df = oa_df.merge(apc_df, on='issn_l', how='left', suffixes=('', '_apc'))
df = df.merge(dj_df[['issn_l','license','apc_source','journal_url']], on='issn_l', how='left')

df['apc_usd'] = df['apc_usd'].fillna(0)
df['oa_type'] = df.apply(classify_oa_type, axis=1)
df['impact_proxy'] = df['2yr_mean_citedness'].fillna(df['cited_by_count'] / df['works_count'])
df['cost_efficiency'] = df.apply(lambda r: float('inf') if r.apc_usd == 0 else r.impact_proxy / r.apc_usd, axis=1)
df['last_verified'] = today_iso
df_final = df[required_columns]
df_final.to_json('public/journals_mvp.json', orient='records')
```

**Client Filtering (TypeScript):**

```ts
const filtered = data
  .filter(d => selectedSubjects.size===0 || selectedSubjects.has(d.subject))
  .filter(d => d.apc_usd <= apcMax && d.impact_proxy >= impactMin)
  .filter(d => selectedOA.size===0 || selectedOA.has(d.oa_type))
  .filter(d => d.title.toLowerCase().includes(searchTerm));
```

---

## PERFORMANCE & USABILITY

* Dataset likely O(10–20K) journals → JSON \~1–2 MB compressed → OK for client load.
* Use **web worker** for heavy filtering if needed.
* Lazy render table rows; pre-compute scales for scatter (D3).
* Provide **loading shimmer** + “Data last updated: YYYY-MM-DD”.
