# Journal Choice Transparency Website - Tasks

## Project Overview
Building a website to help scientists choose journals based on impact metrics, APC costs, and open access status.

## Tasks

### Data Processing & Backend
- [ ] Parse and clean source data files (PDFs, CSV, XLSX) - 2025-07-19
- [ ] Create unified journal dataset with required fields - 2025-07-19
- [ ] Generate final JSON/Parquet file for frontend consumption - 2025-07-19

### Frontend Development
- [ ] Initialize Next.js project with TypeScript and Tailwind - 2025-07-19
- [ ] Create main layout with filter panel and visualization areas - 2025-07-19
- [ ] Implement sortable/filterable data table - 2025-07-19
- [ ] Build interactive scatter plot with tooltips - 2025-07-19
- [ ] Add state synchronization between table and plot - 2025-07-19
- [ ] Implement filtering controls (subject, OA type, APC range, etc.) - 2025-07-19

### Polish & Deployment
- [ ] Add responsive design for mobile - 2025-07-19
- [ ] Optimize performance (virtualization, web workers) - 2025-07-19
- [ ] Add loading states and error handling - 2025-07-19
- [ ] Deploy and test - 2025-07-19

## Completed Tasks
(Tasks will be moved here as they are completed)

## Discovered During Work
(Additional tasks discovered during development will be added here)
