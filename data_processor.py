#!/usr/bin/env python3
"""
Data processing script for Journal Choice Transparency project.
Processes and merges data from multiple sources into a unified dataset.
"""

import pandas as pd
import numpy as np
from pathlib import Path
import json
from datetime import datetime

def clean_mdpi_data():
    """
    Clean and process MDPI APC data.

    Returns:
        pd.DataFrame: Cleaned MDPI data with standardized columns
    """
    print("Processing MDPI data...")
    df = pd.read_csv('source_data/MDPI_APC.csv')

    # Remove empty rows
    df = df.dropna()

    # Clean column names
    df.columns = ['title', 'apc_chf']

    # Extract numeric APC values and convert CHF to USD (approximate rate: 1 CHF = 1.1 USD)
    # Use NA for missing APC data instead of 0
    apc_values = df['apc_chf'].str.extract(r'(\d+)')
    df['apc_usd'] = pd.to_numeric(apc_values[0], errors='coerce') * 1.1

    # Add metadata
    df['publisher'] = 'MDPI'
    df['oa_type'] = 'Fully OA'
    df['source'] = 'MDPI_APC'

    # Clean title
    df['title'] = df['title'].str.strip()

    return df[['title', 'apc_usd', 'publisher', 'oa_type', 'source']]

def clean_scimago_data():
    """
    Clean and process SciMago Journal Ranking data.
    
    Returns:
        pd.DataFrame: Cleaned SciMago data with impact metrics
    """
    print("Processing SciMago data...")
    df = pd.read_csv('source_data/scimagojr 2024.csv', sep=';')
    
    # Clean column names
    df.columns = [col.strip() for col in df.columns]
    
    # Extract relevant columns
    columns_map = {
        'Title': 'title',
        'Issn': 'issn',
        'SJR': 'sjr',
        'H index': 'h_index',
        'Citations / Doc. (2years)': 'citations_per_doc_2yr',
        'Publisher': 'publisher',
        'Categories': 'categories',
        'Areas': 'areas'
    }
    
    df_clean = df[list(columns_map.keys())].copy()
    df_clean.columns = list(columns_map.values())
    
    # Clean numeric columns
    df_clean['sjr'] = pd.to_numeric(df_clean['sjr'].str.replace(',', '.'), errors='coerce')
    df_clean['citations_per_doc_2yr'] = pd.to_numeric(
        df_clean['citations_per_doc_2yr'].str.replace(',', '.'), errors='coerce'
    )
    
    # Use SJR as impact metric (more appropriate than citations per doc)
    df_clean['impact_proxy'] = df_clean['sjr']
    
    # Extract and clean subject from categories - get broader subject areas
    df_clean['subject'] = df_clean['categories'].str.split(';').str[0].str.strip()
    df_clean['subject'] = df_clean['subject'].str.replace(r'\s*\([^)]*\)', '', regex=True)

    # Map to broader subject categories for better filtering
    subject_mapping = {
        'Medicine': ['Medicine', 'Clinical', 'Medical', 'Health', 'Nursing', 'Surgery', 'Therapy'],
        'Life Sciences': ['Biology', 'Biochemistry', 'Genetics', 'Molecular', 'Cell', 'Microbiology', 'Immunology'],
        'Physical Sciences': ['Physics', 'Chemistry', 'Materials', 'Chemical'],
        'Engineering': ['Engineering', 'Computer Science', 'Technology', 'Electronics'],
        'Social Sciences': ['Psychology', 'Sociology', 'Anthropology', 'Political', 'Social'],
        'Business': ['Business', 'Management', 'Economics', 'Finance', 'Accounting'],
        'Environmental': ['Environmental', 'Earth', 'Ecology', 'Climate', 'Geography'],
        'Mathematics': ['Mathematics', 'Statistics', 'Mathematical'],
        'Arts & Humanities': ['Arts', 'Literature', 'History', 'Philosophy', 'Language']
    }

    def categorize_subject(subject_str):
        if pd.isna(subject_str):
            return 'Other'
        subject_str = str(subject_str)
        for category, keywords in subject_mapping.items():
            if any(keyword.lower() in subject_str.lower() for keyword in keywords):
                return category
        return subject_str if len(subject_str) < 30 else 'Other'

    df_clean['subject'] = df_clean['subject'].apply(categorize_subject)
    
    # Clean title
    df_clean['title'] = df_clean['title'].str.strip().str.replace('"', '')
    
    df_clean['source'] = 'SciMago'
    
    return df_clean[['title', 'issn', 'impact_proxy', 'publisher', 'subject', 'source']]



def process_apc_csv_files():
    """
    Process all APC CSV files in source_data directory (excluding SciMago which is impact data).

    Returns:
        pd.DataFrame: Combined APC data from all CSV files
    """
    print("Processing APC CSV files...")

    apc_data = []
    source_dir = Path('source_data')

    # Find all CSV files except SciMago (which is impact data)
    for csv_file in source_dir.glob('*.csv'):
        if csv_file.name in ['MDPI_APC.csv', 'scimagojr 2024.csv']:
            continue  # Skip - processed separately

        print(f"Processing APC file: {csv_file.name}...")
        try:
            # Try different separators and encodings
            df = None
            for sep in [',', ';', '\t']:
                try:
                    df = pd.read_csv(csv_file, sep=sep, encoding='utf-8')
                    if len(df.columns) > 1:  # Valid CSV found
                        break
                except:
                    continue

            if df is not None and len(df) > 0:
                # Use specialized processing for new pdf2csv files
                if 'pdf2csv' in csv_file.name:
                    df_clean = process_pdf2csv_file(df, csv_file.name)
                else:
                    # Use legacy processing for older files
                    df_clean = standardize_apc_data(df, csv_file.name)

                if len(df_clean) > 0:
                    apc_data.append(df_clean)
                    print(f"  Successfully processed {len(df_clean)} APC records from {csv_file.name}")
            else:
                print(f"  Could not read {csv_file.name}")

        except Exception as e:
            print(f"  Error processing {csv_file.name}: {e}")

    if apc_data:
        combined_apc = pd.concat(apc_data, ignore_index=True, sort=False)
        print(f"Total APC records from CSV files: {len(combined_apc)}")
        return combined_apc
    else:
        return pd.DataFrame()

def process_pdf2csv_file(df, filename):
    """
    Process the new pdf2csv files with improved structure.

    Args:
        df: DataFrame from pdf2csv file
        filename: Name of the source file

    Returns:
        pd.DataFrame: Standardized APC data
    """
    result_data = []

    # Handle different pdf2csv file formats
    if 'elsevier' in filename.lower():
        # Elsevier format: ISSN,Title,model,USD,EUR,GBP,JPY
        for _, row in df.iterrows():
            if pd.notna(row.get('Title')) and pd.notna(row.get('USD')):
                record = {
                    'title': str(row['Title']).strip(),
                    'issn_l': str(row.get('ISSN', '')).strip() if pd.notna(row.get('ISSN')) else '',
                    'publisher': 'Elsevier',
                    'source': filename
                }

                # Extract USD APC
                usd_val = str(row['USD']).replace(',', '').replace('"', '')
                try:
                    record['apc_usd'] = float(usd_val)
                except:
                    continue

                # Determine OA type from model
                model = str(row.get('model', '')).lower()
                if 'open access' in model:
                    record['oa_type'] = 'Fully OA'
                elif 'hybrid' in model:
                    record['oa_type'] = 'Hybrid'
                else:
                    record['oa_type'] = 'Hybrid'  # Default for Elsevier

                result_data.append(record)

    elif 'springer_nature_fully' in filename.lower():
        # Springer Nature Fully OA format: Journal title,eISSN,Imprint,2025 EUR,2025 USD,2025 GBP,Website
        for _, row in df.iterrows():
            if pd.notna(row.get('Journal title')) and pd.notna(row.get('2025 USD')):
                record = {
                    'title': str(row['Journal title']).strip(),
                    'issn_l': str(row.get('eISSN', '')).strip() if pd.notna(row.get('eISSN')) else '',
                    'publisher': 'Springer Nature',
                    'oa_type': 'Fully OA',
                    'source': filename
                }

                # Extract USD APC
                usd_val = str(row['2025 USD']).replace(',', '').replace('"', '')
                if 'see website' in usd_val.lower():
                    continue  # Skip entries that require website lookup
                try:
                    record['apc_usd'] = float(usd_val)
                except:
                    continue

                result_data.append(record)

    elif 'springer_nature_hybrid' in filename.lower():
        # Springer Nature Hybrid format: Journal name,eISSN,Imprint,2025 EUR,2025 USD,2025 GBP
        for _, row in df.iterrows():
            if pd.notna(row.get('Journal name')) and pd.notna(row.get('2025 USD')):
                record = {
                    'title': str(row['Journal name']).strip(),
                    'issn_l': str(row.get('eISSN', '')).strip() if pd.notna(row.get('eISSN')) else '',
                    'publisher': 'Springer Nature',
                    'oa_type': 'Hybrid',
                    'source': filename
                }

                # Extract USD APC
                usd_val = str(row['2025 USD']).replace(',', '').replace('"', '')
                try:
                    record['apc_usd'] = float(usd_val)
                except:
                    continue

                result_data.append(record)

    return pd.DataFrame(result_data)

def standardize_apc_data(df, filename):
    """
    Standardize APC data from different CSV sources.

    Args:
        df: DataFrame from CSV file
        filename: Name of the source file

    Returns:
        pd.DataFrame: Standardized APC data
    """
    df_clean = df.copy()

    # Common column mappings
    column_mappings = {
        'journal': ['journal', 'journal_name', 'title', 'journal_title', 'publication'],
        'issn': ['issn', 'issn_l', 'issn-l', 'eissn', 'print_issn', 'online_issn'],
        'apc': ['apc', 'apc_usd', 'apc_eur', 'apc_gbp', 'price', 'cost', 'fee'],
        'publisher': ['publisher', 'publisher_name'],
        'oa_type': ['oa_type', 'open_access_type', 'type', 'model']
    }

    # Find and map columns
    mapped_cols = {}
    for standard_name, possible_names in column_mappings.items():
        for col in df.columns:
            if any(name.lower() in col.lower() for name in possible_names):
                mapped_cols[standard_name] = col
                break

    # Extract and clean data
    result_data = []

    for _, row in df_clean.iterrows():
        record = {}

        # Journal title
        if 'journal' in mapped_cols:
            record['title'] = str(row[mapped_cols['journal']]).strip()
        else:
            continue  # Skip if no journal name

        # ISSN
        if 'issn' in mapped_cols:
            issn_val = str(row[mapped_cols['issn']]).strip()
            if issn_val and issn_val.lower() != 'nan':
                record['issn_l'] = issn_val

        # APC - convert to USD
        if 'apc' in mapped_cols:
            apc_val = row[mapped_cols['apc']]
            if pd.notna(apc_val):
                # Extract numeric value
                if isinstance(apc_val, str):
                    # Remove currency symbols and extract number
                    import re
                    numbers = re.findall(r'[\d,]+\.?\d*', str(apc_val).replace(',', ''))
                    if numbers:
                        try:
                            apc_num = float(numbers[0])
                            # Convert to USD based on currency indicators
                            if 'eur' in str(apc_val).lower() or '€' in str(apc_val):
                                apc_num *= 1.1  # EUR to USD
                            elif 'gbp' in str(apc_val).lower() or '£' in str(apc_val):
                                apc_num *= 1.25  # GBP to USD
                            elif 'chf' in str(apc_val).lower():
                                apc_num *= 1.1  # CHF to USD
                            record['apc_usd'] = apc_num
                        except:
                            pass
                else:
                    try:
                        record['apc_usd'] = float(apc_val)
                    except:
                        pass

        # Publisher
        if 'publisher' in mapped_cols:
            record['publisher'] = str(row[mapped_cols['publisher']]).strip()
        else:
            # Try to infer publisher from filename
            if 'wiley' in filename.lower():
                record['publisher'] = 'Wiley'
            elif 'springer' in filename.lower():
                record['publisher'] = 'Springer Nature'
            elif 'elsevier' in filename.lower():
                record['publisher'] = 'Elsevier'
            else:
                record['publisher'] = 'Unknown'

        # OA Type
        if 'oa_type' in mapped_cols:
            oa_val = str(row[mapped_cols['oa_type']]).lower()
            if 'hybrid' in oa_val:
                record['oa_type'] = 'Hybrid'
            elif any(term in oa_val for term in ['fully', 'gold', 'diamond']):
                record['oa_type'] = 'Fully OA'
            else:
                record['oa_type'] = 'Hybrid'  # Default
        else:
            # Infer from filename
            if 'hybrid' in filename.lower():
                record['oa_type'] = 'Hybrid'
            elif 'open_access' in filename.lower() or 'fully' in filename.lower():
                record['oa_type'] = 'Fully OA'
            else:
                record['oa_type'] = 'Hybrid'  # Default

        record['source'] = filename

        # Only add if we have essential data
        if record.get('title') and ('apc_usd' in record or 'issn_l' in record):
            result_data.append(record)

    return pd.DataFrame(result_data)

def create_unified_dataset():
    """
    Create unified dataset merging SciMago impact data with APC data from CSV sources.

    Returns:
        pd.DataFrame: Unified journal dataset
    """
    print("Creating unified dataset...")

    # Process each data source
    mdpi_df = clean_mdpi_data()
    scimago_df = clean_scimago_data()  # Impact data
    apc_csv_df = process_apc_csv_files()  # APC data from other CSV files

    print(f"MDPI APC records: {len(mdpi_df)}")
    print(f"SciMago impact records: {len(scimago_df)}")
    print(f"Additional APC CSV records: {len(apc_csv_df)}")

    # Combine all APC data sources
    all_apc_data = []
    if len(mdpi_df) > 0:
        all_apc_data.append(mdpi_df[['title', 'apc_usd', 'oa_type', 'publisher', 'source']])
    if len(apc_csv_df) > 0:
        all_apc_data.append(apc_csv_df[['title', 'apc_usd', 'oa_type', 'publisher', 'source']])

    if all_apc_data:
        combined_apc = pd.concat(all_apc_data, ignore_index=True)
        print(f"Total combined APC records: {len(combined_apc)}")
    else:
        combined_apc = pd.DataFrame()

    # Start with SciMago as the base (has impact metrics)
    base_df = scimago_df.copy()

    # Merge APC data with SciMago data by journal title
    if len(combined_apc) > 0:
        # Clean titles for better matching
        base_df['title_clean'] = base_df['title'].str.lower().str.strip()
        combined_apc['title_clean'] = combined_apc['title'].str.lower().str.strip()

        # Merge on cleaned titles
        apc_merge = combined_apc.groupby('title_clean').agg({
            'apc_usd': 'first',  # Take first APC value if multiple
            'oa_type': 'first',
            'publisher': 'first',
            'source': 'first'
        }).reset_index()

        base_df = base_df.merge(apc_merge, on='title_clean', how='left', suffixes=('', '_apc'))

        # Use APC publisher info when available, otherwise keep SciMago publisher
        base_df['publisher'] = base_df['publisher_apc'].fillna(base_df['publisher'])
        base_df = base_df.drop(['publisher_apc', 'title_clean'], axis=1)

        print(f"Journals with APC data after merge: {base_df['apc_usd'].notna().sum()}")

    # Classify OA type more accurately - simplify to Fully OA, Hybrid, or Subscription
    def classify_oa_type(row):
        if pd.notna(row['oa_type']):
            oa_type = str(row['oa_type']).lower()
            if 'fully' in oa_type or 'diamond' in oa_type or 'gold' in oa_type:
                return 'Fully OA'
            elif 'hybrid' in oa_type:
                return 'Hybrid'

        # Classify based on APC data availability
        if pd.notna(row['apc_usd']):
            if row['apc_usd'] > 0:
                return 'Hybrid'  # Has APC cost, likely hybrid
            elif row['apc_usd'] == 0:
                return 'Fully OA'  # No APC cost, likely fully OA

        # If no APC data and no explicit OA type, likely subscription-only
        return 'Subscription'

    base_df['oa_type'] = base_df.apply(classify_oa_type, axis=1)

    # Calculate cost efficiency using impact proxy - handle NA values properly
    def calculate_cost_efficiency(row):
        if pd.isna(row['apc_usd']) or pd.isna(row['impact_proxy']):
            return np.nan
        elif row['apc_usd'] == 0:
            return float('inf')
        else:
            return row['impact_proxy'] / row['apc_usd']

    base_df['cost_efficiency'] = base_df.apply(calculate_cost_efficiency, axis=1)

    # Add metadata
    base_df['last_verified'] = datetime.now().strftime('%Y-%m-%d')
    base_df['license'] = 'Unknown'
    base_df['waiver'] = False
    base_df['journal_url'] = ''

    # Create ISSN-L (use first ISSN if multiple)
    base_df['issn_l'] = base_df['issn'].str.split(',').str[0].str.strip()

    # Final column selection and ordering
    final_columns = [
        'issn_l', 'title', 'subject', 'impact_proxy', 'apc_usd',
        'oa_type', 'license', 'waiver', 'cost_efficiency',
        'last_verified', 'journal_url', 'publisher'
    ]

    result_df = base_df[final_columns].copy()

    # Remove rows with missing critical data (but keep those with missing APC)
    result_df = result_df.dropna(subset=['title', 'impact_proxy'])

    # Filter subject areas to only include those with at least 10 journals with APC data
    print("Filtering subject areas with sufficient APC data...")

    # Count journals with APC data per subject - require >20 journals
    subject_apc_counts = result_df[result_df['apc_usd'].notna()].groupby('subject').size()
    valid_subjects = subject_apc_counts[subject_apc_counts > 20].index.tolist()

    print(f"Subject areas with >20 APC journals: {len(valid_subjects)}")
    for subject in valid_subjects:
        count = subject_apc_counts[subject]
        print(f"  {subject}: {count} journals with APC data")

    # Keep only journals in valid subject areas
    result_df = result_df[result_df['subject'].isin(valid_subjects)]

    print(f"Final unified dataset: {len(result_df)} records")
    print(f"Records with APC data: {result_df['apc_usd'].notna().sum()}")
    print(f"Records with missing APC: {result_df['apc_usd'].isna().sum()}")
    print(f"Subject areas included: {result_df['subject'].nunique()}")

    return result_df

def main():
    """Main processing function."""
    print("Journal Choice Transparency - Data Processing")
    print("=" * 50)
    
    # Create unified dataset
    unified_df = create_unified_dataset()
    
    # Save as JSON for frontend
    output_path = Path('public')
    output_path.mkdir(exist_ok=True)
    
    # Clean data before JSON conversion
    # Replace NaN values with appropriate defaults, but keep APC as null when missing
    unified_df = unified_df.fillna({
        'title': 'Unknown',
        'subject': 'Unknown',
        'impact_proxy': 0,
        'oa_type': 'Unknown',
        'license': 'Unknown',
        'waiver': False,
        'journal_url': '',
        'publisher': 'Unknown',
        'issn_l': ''
    })
    # Don't fill apc_usd - keep as NaN/null for missing data

    # Convert to JSON format, handling infinity and NaN values
    journals_data = unified_df.to_dict('records')

    # Clean up problematic values for JSON compatibility
    for journal in journals_data:
        # Handle infinity values
        if pd.notna(journal['cost_efficiency']) and journal['cost_efficiency'] == float('inf'):
            journal['cost_efficiency'] = 999999

        # Handle NaN values - convert to null for JSON
        for key, value in journal.items():
            if pd.isna(value):
                if key == 'apc_usd':
                    journal[key] = None  # Keep as null for missing APC data
                elif key in ['impact_proxy', 'cost_efficiency']:
                    journal[key] = 0
                elif key == 'waiver':
                    journal[key] = False
                else:
                    journal[key] = 'Unknown'
            elif isinstance(value, float) and not np.isfinite(value):
                if key in ['impact_proxy', 'cost_efficiency']:
                    journal[key] = 0
                elif key == 'apc_usd':
                    journal[key] = None
                else:
                    journal[key] = 'Unknown'

    with open(output_path / 'journals_mvp.json', 'w') as f:
        json.dump(journals_data, f, indent=2, ensure_ascii=False)
    
    # Also save as CSV for inspection
    unified_df.to_csv(output_path / 'journals_mvp.csv', index=False)
    
    print(f"Saved {len(journals_data)} journals to public/journals_mvp.json")
    print(f"Also saved CSV version to public/journals_mvp.csv")
    
    # Print sample data
    print("\nSample data:")
    print(unified_df.head(3).to_string())

if __name__ == "__main__":
    main()
